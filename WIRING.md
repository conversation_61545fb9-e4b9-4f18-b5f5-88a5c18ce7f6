# 🔌 S<PERSON> đồ kết nối chi tiết

## 📋 Bảng kết nối tổng quan

| Thiết bị | Chân thiết bị | ESP8266 (NodeMCU) | GPIO | Ghi chú |
|----------|---------------|-------------------|------|---------|
| **HY-SRF05** | VCC | 3.3V hoặc 5V | - | Nguồn cảm biến |
| | GND | GND | - | Đất chung |
| | Trig | D2 | GPIO4 | Chân phát xung |
| | Echo | D1 | GPIO5 | Chân nhận echo |
| **Relay** | VCC | 3.3V | - | Nguồn relay |
| | GND | GND | - | Đất chung |
| | IN | D5 | GPIO14 | Chân điều khiển |
| **Button** | Chân 1 | D6 | GPIO12 | Nút nhấn |
| | Chân 2 | GND | - | Đất |
| **Bơm** | Dương (+) | COM Relay | - | Qua relay |
| | Âm (-) | Nguồn (-) | - | Trực tiếp |

## 🔧 Kết nối từng bước

### 1. Cảm biến HY-SRF05
```
HY-SRF05 Pin Layout:
┌─────────────────┐
│  VCC  GND  Trig │
│              Echo│
└─────────────────┘

Kết nối:
VCC  → NodeMCU 3.3V (hoặc 5V nếu cảm biến yêu cầu)
GND  → NodeMCU GND
Trig → NodeMCU D2 (GPIO4)
Echo → NodeMCU D1 (GPIO5)
```

**⚠️ Lưu ý**: 
- HY-SRF05 có thể hoạt động với 3.3V hoặc 5V
- Nếu dùng 5V, cần kiểm tra mức logic của chân Echo
- Khoảng cách đo: 2cm - 450cm

### 2. Relay Module
```
Relay Module Pin Layout:
┌─────────────────────┐
│ VCC  GND  IN       │
│                     │
│ COM  NO   NC        │
└─────────────────────┘

Kết nối điều khiển:
VCC → NodeMCU 3.3V
GND → NodeMCU GND  
IN  → NodeMCU D5 (GPIO14)

Kết nối bơm:
COM → Dây dương (+) của bơm
NO  → Dây dương (+) từ nguồn
NC  → Không dùng
```

**⚠️ Lưu ý**:
- Relay cần nguồn riêng cho coil (3.3V từ ESP8266)
- Bơm cần nguồn riêng (12V/24V tùy loại bơm)
- Sử dụng chân NO (Normally Open) để bơm tắt khi relay không có tín hiệu

### 3. Nút nhấn vật lý
```
Push Button:
┌─────┐
│  ●  │ ← Chân 1 → NodeMCU D6 (GPIO12)
│     │
│  ●  │ ← Chân 2 → NodeMCU GND
└─────┘

Hoặc với điện trở pull-up ngoài:
NodeMCU D6 ──┬── Button ── GND
              │
              └── 10kΩ ── 3.3V
```

**⚠️ Lưu ý**:
- Code sử dụng INPUT_PULLUP nên không cần điện trở ngoài
- Nút nhấn kéo chân xuống GND khi nhấn
- Thời gian nhấn tối thiểu: 500ms

## ⚡ Sơ đồ nguồn cấp

### Nguồn cho ESP8266:
- **USB**: 5V từ máy tính (khi lập trình)
- **Adapter**: 5V/1A qua jack DC hoặc Vin
- **Pin**: 3.7V Li-ion qua chân 3.3V (không khuyến khích)

### Nguồn cho bơm nước:
```
Adapter 12V/24V ──┬── Relay COM
                  │
                  └── Bơm nước (-)

Bơm nước (+) ── Relay NO
```

**⚠️ Quan trọng**:
- ESP8266 và bơm phải dùng nguồn riêng biệt
- Chung GND giữa ESP8266 và relay
- Bơm lớn cần nguồn có dòng cao (2-5A)

## 🛡️ Bảo vệ mạch

### 1. Bảo vệ ESP8266:
- **Tụ lọc**: 100µF giữa VCC và GND
- **Điện trở hạn dòng**: 220Ω cho LED báo hiệu (nếu có)

### 2. Bảo vệ relay:
- **Diode bảo vệ**: 1N4007 song song với coil relay
- **Tụ lọc**: 10µF giữa VCC và GND của relay

### 3. Bảo vệ cảm biến:
- **Tụ lọc**: 10µF gần chân VCC của cảm biến
- **Dây ngắn**: Giữ dây kết nối ngắn nhất có thể

## 📐 Lắp đặt cảm biến

### Vị trí cảm biến:
```
Bể nước (nhìn từ trên):
┌─────────────────────┐
│                     │
│    [HY-SRF05]      │ ← Cảm biến ở giữa, cách thành bể >5cm
│                     │
│                     │
│                     │
└─────────────────────┘

Bể nước (nhìn từ bên):
     [HY-SRF05]
         │
         ▼ (5cm tối thiểu)
    ┌─────────────┐
    │ ~~~~~~~~~~~ │ ← Mặt nước
    │ ~~~~~~~~~~~ │
    │ ~~~~~~~~~~~ │
    │             │
    └─────────────┘
```

### Yêu cầu lắp đặt:
- **Khoảng cách tối thiểu**: 5cm từ cảm biến đến mặt nước
- **Góc nghiêng**: Cảm biến vuông góc với mặt nước
- **Che chắn**: Tránh nước bắn vào cảm biến
- **Cố định**: Gắn chắc chắn, không rung lắc

## 🔧 Kiểm tra kết nối

### 1. Kiểm tra nguồn:
```bash
# Đo điện áp các điểm:
ESP8266 3.3V pin: 3.2-3.4V
Relay VCC: 3.2-3.4V  
Cảm biến VCC: 3.2-3.4V hoặc 4.8-5.2V
```

### 2. Kiểm tra tín hiệu:
```bash
# Upload code và mở Serial Monitor
pio device monitor

# Kiểm tra log:
✅ Đã khởi tạo các chân GPIO
📏 Cảm biến: TRIG=4, ECHO=5
🚰 Bơm nước: PIN=14
🔘 Nút nhấn: PIN=12
💧 Mực nước: XX.X cm
```

### 3. Test từng thiết bị:
- **Cảm biến**: Che tay lên cảm biến, xem số đo thay đổi
- **Relay**: Nghe tiếng "click" khi bật/tắt bơm
- **Nút nhấn**: Nhấn giữ >500ms, kiểm tra log
- **Bơm**: Kiểm tra bơm chạy khi relay bật

## ⚠️ Lưu ý an toàn

1. **Điện**: Ngắt nguồn khi đấu dây
2. **Nước**: Cách ly mạch điện khỏi nước
3. **Nguồn**: Không nối nhầm cực tính
4. **Relay**: Kiểm tra khả năng chịu tải
5. **Bơm**: Đảm bảo bơm có nước khi chạy (tránh chạy khô)

## 🔍 Troubleshooting kết nối

| Vấn đề | Nguyên nhân | Giải pháp |
|--------|-------------|-----------|
| Cảm biến trả về -1 | Dây lỏng, nguồn yếu | Kiểm tra kết nối, nguồn |
| Relay không click | Chân điều khiển lỗi | Kiểm tra GPIO14, nguồn relay |
| Bơm không chạy | Relay lỗi, nguồn bơm | Kiểm tra relay, nguồn bơm |
| Nút không hoạt động | Pull-up lỗi | Kiểm tra GPIO12, GND |
| ESP reset liên tục | Nguồn không đủ | Dùng nguồn 5V/2A |
