# 🔧 Hướng dẫn khắc phục sự cố

## 🚨 Các vấn đề thường gặp

### 1. 📏 Cảm biến đọc giá trị cố định (95.0 cm)

**Nguyên nhân:**
- Cảm biến chưa được kết nối đúng
- <PERSON><PERSON>ồ<PERSON> cấp không đủ
- Chân ECHO hoặc TRIG bị lỗi
- Kho<PERSON>ng cách đo ngoài phạm vi

**Cách khắc phục:**

#### Bước 1: Kiểm tra kết nối
```
HY-SRF05 → NodeMCU
VCC      → 3.3V (hoặc 5V)
GND      → GND  
Trig     → D2 (GPIO4)
Echo     → D1 (GPIO5)
```

#### Bước 2: Kiểm tra nguồn
- Đo điện áp VCC của cảm biến: 3.2-3.4V hoặc 4.8-5.2V
- <PERSON><PERSON><PERSON> bả<PERSON> GND chung giữa ESP8266 và cảm biến

#### Bước 3: Test cảm biến
```cpp
// Thêm vào loop() để test
Serial.println("TRIG=" + String(digitalRead(TRIG_PIN)));
Serial.println("ECHO=" + String(digitalRead(ECHO_PIN)));
```

#### Bước 4: Kiểm tra khoảng cách
- Cảm biến hoạt động trong phạm vi 2cm - 400cm
- Đặt vật cản ở khoảng cách khác nhau để test
- Đảm bảo bề mặt phản xạ tốt (không hấp thụ âm thanh)

### 2. 🚰 Bơm không hoạt động

**Nguyên nhân:**
- Relay không được cấp nguồn
- Kết nối relay sai
- Bơm không có nguồn riêng
- Chân điều khiển relay lỗi

**Cách khắc phục:**

#### Bước 1: Kiểm tra relay
```
Relay Module → NodeMCU
VCC          → 3.3V
GND          → GND
IN           → D5 (GPIO14)
```

#### Bước 2: Test relay
- Nghe tiếng "click" khi bật/tắt
- Đo điện áp giữa COM và NO khi relay bật
- Kiểm tra LED báo hiệu trên relay module

#### Bước 3: Kiểm tra bơm
```
Bơm 12V/24V → Relay → Nguồn
Dương (+)   → COM   → 
Âm (-)      → GND nguồn
            → NO    → Dương nguồn
```

#### Bước 4: Debug code
Xem log Serial Monitor:
```
🚰 Bơm nước: BẬT | Relay PIN 14 = HIGH
```

### 3. 🔘 Nút nhấn hoạt động sai

**Vấn đề hiện tại:** Nút toggle on/off thay vì nhấn giữ để bơm

**Đã sửa trong code mới:**
- Nhấn nút = Bật bơm ngay lập tức
- Thả nút = Tắt bơm ngay lập tức
- Có debounce chống nhiễu

**Kiểm tra kết nối:**
```
Button → NodeMCU
Chân 1 → D6 (GPIO12)
Chân 2 → GND
```

### 4. 🌐 Không truy cập được web

**Nguyên nhân:**
- WiFi không kết nối
- IP address thay đổi
- mDNS không hoạt động

**Cách khắc phục:**

#### Bước 1: Kiểm tra WiFi
```
✅ WiFi đã kết nối thành công!
🌐 Địa chỉ IP: 192.168.1.xxx
```

#### Bước 2: Truy cập bằng IP
- Mở Serial Monitor để xem IP
- Truy cập trực tiếp: `http://192.168.1.xxx`

#### Bước 3: Sử dụng AP mode
- Nếu WiFi lỗi, ESP8266 tự tạo AP
- Kết nối WiFi: `ESP8266-WaterLevel`
- Mật khẩu: `12345678`
- Truy cập: `http://***********`

## 🔍 Debug bằng Serial Monitor

### Mở Serial Monitor:
```bash
pio device monitor
```

### Log bình thường:
```
💧 Hệ Thống Đo Mực Nước ESP8266
✅ Đã khởi tạo các chân GPIO
🧪 Test relay...
   Relay HIGH: OK
   Relay LOW: OK
🧪 Test cảm biến...
   Cảm biến: OK - Mực nước: 45.2cm
✅ WiFi đã kết nối thành công!
🌐 Địa chỉ IP: *************
💧 Mực nước: 45.2 cm
```

### Log có lỗi:
```
⚠️ Cảm biến không phản hồi! Kiểm tra kết nối TRIG=4, ECHO=5
❌ Lỗi đọc cảm biến lần 1
⚠️ CẢNH BÁO: Trạng thái chân không khớp với lệnh!
```

## 🛠️ Các bước debug từng bước

### 1. Kiểm tra phần cứng
```bash
# Upload code và mở Serial Monitor
pio run --target upload
pio device monitor

# Quan sát log khởi tạo
# Kiểm tra từng test: relay, nút nhấn, cảm biến
```

### 2. Test từng chức năng

#### Test cảm biến:
- Che tay lên cảm biến
- Xem số đo thay đổi trong Serial Monitor
- Khoảng cách hợp lệ: 2-400cm

#### Test relay:
- Bật/tắt bơm từ web
- Nghe tiếng "click" của relay
- Kiểm tra LED báo hiệu

#### Test nút nhấn:
- Nhấn giữ nút vật lý
- Xem log: "🔘 Nút nhấn: BẮT ĐẦU BƠM"
- Thả nút, xem log: "🔘 Nút nhấn: DỪNG BƠM"

### 3. Kiểm tra web interface
- Truy cập `http://waterlevel.local`
- Kiểm tra mực nước hiển thị
- Test nút bật/tắt bơm
- Xem auto-update mỗi 2 giây

## ⚡ Các lỗi thường gặp và giải pháp

| Lỗi | Nguyên nhân | Giải pháp |
|-----|-------------|-----------|
| Cảm biến trả về -1 | Không có tín hiệu echo | Kiểm tra dây ECHO, nguồn |
| Relay không click | Chân điều khiển lỗi | Kiểm tra GPIO14, nguồn 3.3V |
| Bơm không chạy | Nguồn bơm không có | Kiểm tra nguồn 12V/24V riêng |
| Nút không phản hồi | Pull-up lỗi | Kiểm tra GPIO12, GND |
| Web không load | WiFi lỗi | Dùng AP mode hoặc kiểm tra IP |
| ESP reset liên tục | Nguồn yếu | Dùng nguồn 5V/2A |

## 🔧 Cấu hình nâng cao

### Thay đổi thông số cảm biến:
```cpp
const float TANK_HEIGHT = 100.0;  // Chiều cao bể (cm)
const float MIN_DISTANCE = 5.0;   // Khoảng cách tối thiểu
const float MAX_DISTANCE = 95.0;  // Khoảng cách tối đa
```

### Thay đổi chân GPIO:
```cpp
const int TRIG_PIN = 4;    // Chân Trigger
const int ECHO_PIN = 5;    // Chân Echo
const int PUMP_PIN = 14;   // Chân relay
const int BUTTON_PIN = 12; // Chân nút nhấn
```

### Thay đổi thời gian:
```cpp
const unsigned long SENSOR_INTERVAL = 1000; // Đọc cảm biến (ms)
const unsigned long BUTTON_HOLD_TIME = 500; // Debounce nút (ms)
```

## 📞 Hỗ trợ thêm

### Nếu vẫn gặp vấn đề:

1. **Chụp ảnh kết nối** phần cứng
2. **Copy log** từ Serial Monitor
3. **Mô tả chi tiết** hiện tượng lỗi
4. **Thông tin board** ESP8266 đang dùng

### Thông tin hữu ích:
- **Board**: NodeMCU, Wemos D1 Mini, ESP-12E?
- **Nguồn**: USB, adapter 5V, pin?
- **Cảm biến**: HY-SRF05, HC-SR04?
- **Relay**: Module relay 1 kênh, 2 kênh?
- **Bơm**: 12V, 24V, dòng tiêu thụ?

### Debug nâng cao:
```cpp
// Thêm vào code để debug chi tiết
#define DEBUG_SENSOR true
#define DEBUG_RELAY true
#define DEBUG_BUTTON true
```
