/*
 * File cấu hình mẫu cho Hệ Thống Đo Mực Nước ESP8266
 * 
 * Hướng dẫn sử dụng:
 * 1. Copy file này thành config.h
 * 2. Thay đổi các thông số phù hợp với hệ thống của bạn
 * 3. Include config.h vào main.cpp
 */

#ifndef CONFIG_H
#define CONFIG_H

// ===== CẤU HÌNH WIFI =====
#define WIFI_SSID "TEN_WIFI_CUA_BAN"        // Thay đổi tên WiFi
#define WIFI_PASSWORD "MAT_KHAU_WIFI"       // Thay đổi mật khẩu WiFi

// ===== CẤU HÌNH ACCESS POINT =====
#define AP_SSID "ESP8266-WaterLevel"        // Tên WiFi khi ESP làm AP
#define AP_PASSWORD "12345678"              // Mật khẩu AP (tối thiểu 8 ký tự)
#define AP_CHANNEL 6                        // Kênh WiFi (1-13)
#define AP_MAX_CONNECTIONS 4                // Số kết nối tối đa
#define AP_HIDDEN false                     // Ẩn SSID hay không

// ===== CẤU HÌNH mDNS =====
#define MDNS_NAME "waterlevel"              // Truy cập qua http://waterlevel.local

// ===== CẤU HÌNH CHÂN GPIO =====
// Cảm biến siêu âm HY-SRF05
#define TRIG_PIN 4                          // GPIO4 (D2 trên NodeMCU)
#define ECHO_PIN 5                          // GPIO5 (D1 trên NodeMCU)

// Bơm nước (relay)
#define PUMP_PIN 14                         // GPIO14 (D5 trên NodeMCU)

// Nút nhấn vật lý
#define BUTTON_PIN 12                       // GPIO12 (D6 trên NodeMCU)

// ===== CẤU HÌNH BỂ CHỨA NƯỚC =====
#define TANK_HEIGHT 100.0                   // Chiều cao bể chứa (cm)
#define MIN_DISTANCE 5.0                    // Khoảng cách tối thiểu từ cảm biến (cm)
#define MAX_DISTANCE 95.0                   // Khoảng cách tối đa từ cảm biến (cm)

// ===== CẤU HÌNH THỜI GIAN =====
#define SENSOR_READ_INTERVAL 1000           // Đọc cảm biến mỗi 1 giây (ms)
#define BUTTON_HOLD_TIME 500                // Thời gian nhấn giữ tối thiểu (ms)
#define WIFI_CHECK_INTERVAL_NORMAL 30000    // Kiểm tra WiFi mỗi 30 giây (ms)
#define WIFI_CHECK_INTERVAL_AP 120000       // Kiểm tra WiFi mỗi 2 phút khi ở AP mode (ms)

// ===== CẤU HÌNH NÂNG CAO =====
// Cảm biến
#define SENSOR_TIMEOUT 30000                // Timeout đọc cảm biến (microseconds)
#define SENSOR_SAMPLES 3                    // Số lần đọc để lấy trung bình
#define SENSOR_DELAY_BETWEEN_SAMPLES 50     // Delay giữa các lần đọc (ms)

// Bơm nước
#define PUMP_MAX_RUN_TIME 300000            // Thời gian chạy tối đa (5 phút)
#define PUMP_COOLDOWN_TIME 60000            // Thời gian nghỉ giữa các lần bơm (1 phút)

// Web server
#define WEB_UPDATE_INTERVAL 2000            // Cập nhật web mỗi 2 giây (ms)
#define API_RESPONSE_TIMEOUT 5000           // Timeout cho API response (ms)

// ===== CẤU HÌNH TỰ ĐỘNG HÓA (TÙY CHỌN) =====
// Bỏ comment để bật tính năng tự động
// #define ENABLE_AUTO_PUMP                    // Bật tự động bơm
// #define AUTO_PUMP_LOW_LEVEL 20.0            // Mực nước thấp để bật bơm (cm)
// #define AUTO_PUMP_HIGH_LEVEL 80.0           // Mực nước cao để tắt bơm (cm)
// #define AUTO_PUMP_CHECK_INTERVAL 10000      // Kiểm tra tự động mỗi 10 giây

// ===== CẤU HÌNH CẢNH BÁO (TÙY CHỌN) =====
// #define ENABLE_ALERTS                       // Bật cảnh báo
// #define ALERT_LOW_LEVEL 10.0                // Mực nước thấp cảnh báo (cm)
// #define ALERT_HIGH_LEVEL 90.0               // Mực nước cao cảnh báo (cm)
// #define ALERT_SENSOR_ERROR_COUNT 5          // Số lần lỗi cảm biến liên tiếp để cảnh báo

// ===== CẤU HÌNH DEBUG =====
#define ENABLE_SERIAL_DEBUG true            // Bật debug qua Serial
#define SERIAL_BAUD_RATE 115200             // Tốc độ Serial
#define DEBUG_SENSOR_READINGS false         // Debug chi tiết đọc cảm biến
#define DEBUG_WIFI_CONNECTION true          // Debug kết nối WiFi
#define DEBUG_WEB_REQUESTS false            // Debug web requests

// ===== CẤU HÌNH BẢO MẬT (TÙY CHỌN) =====
// #define ENABLE_WEB_AUTH                     // Bật xác thực web
// #define WEB_USERNAME "admin"                // Tên đăng nhập
// #define WEB_PASSWORD "password"             // Mật khẩu web

// ===== CẤU HÌNH MQTT (TÙY CHỌN) =====
// #define ENABLE_MQTT                         // Bật MQTT
// #define MQTT_SERVER "192.168.1.100"         // Địa chỉ MQTT broker
// #define MQTT_PORT 1883                      // Port MQTT
// #define MQTT_USERNAME "mqtt_user"           // MQTT username
// #define MQTT_PASSWORD "mqtt_pass"           // MQTT password
// #define MQTT_TOPIC_PREFIX "waterlevel"      // Prefix cho topics

// ===== VALIDATION CẤU HÌNH =====
// Kiểm tra các giá trị hợp lệ
#if TANK_HEIGHT <= 0
#error "TANK_HEIGHT phải lớn hơn 0"
#endif

#if MIN_DISTANCE >= MAX_DISTANCE
#error "MIN_DISTANCE phải nhỏ hơn MAX_DISTANCE"
#endif

#if MAX_DISTANCE >= TANK_HEIGHT
#error "MAX_DISTANCE phải nhỏ hơn TANK_HEIGHT"
#endif

#if SENSOR_READ_INTERVAL < 100
#error "SENSOR_READ_INTERVAL không nên nhỏ hơn 100ms"
#endif

#if BUTTON_HOLD_TIME < 50
#error "BUTTON_HOLD_TIME không nên nhỏ hơn 50ms"
#endif

// ===== MACRO TIỆN ÍCH =====
#define DEBUG_PRINT(x) if(ENABLE_SERIAL_DEBUG) Serial.print(x)
#define DEBUG_PRINTLN(x) if(ENABLE_SERIAL_DEBUG) Serial.println(x)
#define DEBUG_PRINTF(format, ...) if(ENABLE_SERIAL_DEBUG) Serial.printf(format, ##__VA_ARGS__)

// Chuyển đổi đơn vị
#define CM_TO_PERCENT(cm) ((cm / TANK_HEIGHT) * 100.0)
#define PERCENT_TO_CM(percent) ((percent / 100.0) * TANK_HEIGHT)

// Kiểm tra mức nước
#define IS_WATER_LOW(level) (level < AUTO_PUMP_LOW_LEVEL)
#define IS_WATER_HIGH(level) (level > AUTO_PUMP_HIGH_LEVEL)
#define IS_WATER_CRITICAL_LOW(level) (level < ALERT_LOW_LEVEL)
#define IS_WATER_CRITICAL_HIGH(level) (level > ALERT_HIGH_LEVEL)

#endif // CONFIG_H

/*
 * Ghi chú sử dụng:
 * 
 * 1. CẤU HÌNH CƠ BẢN:
 *    - Thay đổi WIFI_SSID và WIFI_PASSWORD
 *    - Điều chỉnh TANK_HEIGHT theo bể của bạn
 *    - Kiểm tra các chân GPIO phù hợp với board
 * 
 * 2. CẤU HÌNH NÂNG CAO:
 *    - Bỏ comment các tính năng muốn sử dụng
 *    - Điều chỉnh thời gian và ngưỡng phù hợp
 *    - Bật debug khi cần troubleshoot
 * 
 * 3. BẢO MẬT:
 *    - Bật WEB_AUTH nếu cần bảo mật
 *    - Thay đổi mật khẩu mặc định
 *    - Sử dụng HTTPS nếu có thể
 * 
 * 4. TÍCH HỢP:
 *    - Bật MQTT để tích hợp Home Assistant
 *    - Cấu hình alerts cho giám sát
 *    - Sử dụng auto pump cho tự động hóa
 */
