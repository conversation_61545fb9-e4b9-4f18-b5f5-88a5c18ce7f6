# 💧 Hệ Thống Đo Mực Nước ESP8266

Dự án ESP8266 để đo mực nước bằng cảm biến siêu âm HY-SRF05 và điều khiển bơm nước với giao diện web.

## 🔧 Linh kiện cần thiết

### Phần cứng chính:
- **ESP8266** (NodeMCU hoặc Wemos D1 Mini)
- **Cảm biến siêu âm HY-SRF05** (hoặc HC-SR04)
- **Relay module 5V** (để điều khiển bơm)
- **Bơm nước 12V/24V**
- **Nút nhấn** (push button)
- **<PERSON><PERSON><PERSON><PERSON> cấp** phù hợp cho bơm

### Linh kiện phụ:
- Dây jumper
- Breadboard hoặc PCB
- Điện trở pull-up 10kΩ (nếu cần)
- T<PERSON> điện lọc nhiễ<PERSON> (tù<PERSON> chọ<PERSON>)

## 📋 Sơ đồ kết nối

### Cảm biến HY-SRF05:
```
HY-SRF05    →    ESP8266 (NodeMCU)
VCC         →    3.3V hoặc 5V
GND         →    GND
Trig        →    D2 (GPIO4)
Echo        →    D1 (GPIO5)
```

### Relay điều khiển bơm:
```
Relay       →    ESP8266 (NodeMCU)
VCC         →    3.3V
GND         →    GND
IN          →    D5 (GPIO14)
```

### Nút nhấn vật lý:
```
Button      →    ESP8266 (NodeMCU)
Một chân    →    D6 (GPIO12)
Chân kia    →    GND
```

### Bơm nước:
```
Bơm 12V/24V  →  Relay
Dương (+)    →  COM của Relay
Âm (-)       →  Nguồn âm
Nguồn dương  →  NO của Relay
```

## ⚙️ Cấu hình

### 1. Cấu hình WiFi
Trong file `src/main.cpp`, thay đổi thông tin WiFi:
```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";
const char* password = "MAT_KHAU_WIFI";
```

### 2. Cấu hình thông số bể chứa
```cpp
const float TANK_HEIGHT = 100.0;  // Chiều cao bể (cm)
const float MIN_DISTANCE = 5.0;   // Khoảng cách tối thiểu (cm)
const float MAX_DISTANCE = 95.0;  // Khoảng cách tối đa (cm)
```

### 3. Cấu hình chân GPIO (nếu cần thay đổi)
```cpp
const int TRIG_PIN = 4;    // Chân Trigger
const int ECHO_PIN = 5;    // Chân Echo  
const int PUMP_PIN = 14;   // Chân điều khiển relay
const int BUTTON_PIN = 12; // Chân nút nhấn
```

## 🚀 Cách sử dụng

### 1. Upload code
```bash
pio run --target upload
```

### 2. Kết nối WiFi
- ESP8266 sẽ tự động kết nối WiFi đã cấu hình
- Nếu không kết nối được, sẽ tạo Access Point: `ESP8266-WaterLevel`
- Mật khẩu AP: `12345678`

### 3. Truy cập giao diện web
- **Qua WiFi**: `http://waterlevel.local` hoặc IP của ESP8266
- **Qua AP**: `http://***********`

### 4. Điều khiển

#### Giao diện web:
- **Xem mực nước**: Hiển thị real-time với thanh nước trực quan
- **Bật/Tắt bơm**: Nút điều khiển trên giao diện
- **Tự động cập nhật**: Mực nước cập nhật mỗi 2 giây

#### Nút vật lý:
- **Nhấn giữ nút**: Bật/tắt bơm nước
- **Thời gian nhấn tối thiểu**: 500ms

## 📊 Tính năng

### ✅ Đã hoàn thành:
- [x] Đo mực nước bằng HY-SRF05
- [x] Hiển thị mực nước trên giao diện web
- [x] Điều khiển bơm qua web
- [x] Nút nhấn vật lý để điều khiển bơm
- [x] Giao diện responsive (mobile-friendly)
- [x] Auto-reconnect WiFi
- [x] Access Point fallback
- [x] mDNS support
- [x] Real-time update mực nước

### 🔄 Có thể mở rộng:
- [ ] Tự động bơm khi mực nước thấp
- [ ] Cảnh báo qua email/SMS
- [ ] Lưu lịch sử mực nước
- [ ] Tích hợp MQTT
- [ ] Cảnh báo tràn nước
- [ ] Lập lịch bơm tự động

## 🛠️ Troubleshooting

### Cảm biến không hoạt động:
- Kiểm tra kết nối dây
- Đảm bảo nguồn cấp đủ (3.3V hoặc 5V)
- Kiểm tra khoảng cách đo (5-400cm)

### Bơm không hoạt động:
- Kiểm tra relay và nguồn cấp bơm
- Đảm bảo relay được cấp nguồn đúng
- Kiểm tra kết nối chân điều khiển

### Không kết nối được WiFi:
- Kiểm tra tên WiFi và mật khẩu
- Kết nối vào AP mode để cấu hình
- Reset ESP8266 và thử lại

## 📱 Giao diện

Giao diện web được tối ưu cho:
- **Desktop**: Hiển thị đầy đủ thông tin
- **Mobile**: Layout responsive, dễ sử dụng
- **Dark theme**: Giao diện tối, dễ nhìn
- **Real-time**: Cập nhật tự động

## 🔒 Bảo mật

- Chỉ hoạt động trong mạng LAN
- Không có authentication (phù hợp cho mạng gia đình)
- Có thể thêm basic auth nếu cần

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Serial Monitor để xem log
2. Kết nối phần cứng
3. Cấu hình WiFi
4. Nguồn cấp đủ cho các thiết bị
