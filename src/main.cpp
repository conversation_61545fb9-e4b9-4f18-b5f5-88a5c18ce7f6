#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <ESP8266mDNS.h>

extern "C" {
#include "user_interface.h"
}

// ===== CẤU HÌNH WIFI =====
const char* ssid = "ABC";           // Thay đổi tên WiFi của bạn
const char* password = "88888888";  // Thay đổi mật khẩu WiFi của bạn

// ===== CẤU HÌNH ACCESS POINT =====
const char* ap_ssid = "ESP8266-WaterLevel";   // Tên WiFi của ESP khi làm AP
const char* ap_password = "12345678";         // Mật khẩu WiFi của ESP (tối thiểu 8 ký tự)

// ===== CẤU HÌNH AP NÂNG CAO =====
const int ap_channel = 6;                     // Kênh WiFi (1-13, tr<PERSON><PERSON> xung đột)
const int ap_max_connections = 4;             // Số kết nối tối đa
const bool ap_hidden = false;                 // Ẩn SSID hay không

// ===== CẤU HÌNH mDNS =====
const char* mdns_name = "waterlevel";         // Truy cập qua http://waterlevel.local

// ===== BIẾN TRẠNG THÁI =====
bool isWiFiConnected = false;       // Trạng thái kết nối WiFi
bool isAPMode = false;              // Trạng thái chế độ AP
unsigned long lastAPCheck = 0;      // Thời gian kiểm tra AP cuối cùng
unsigned long apStartTime = 0;      // Thời gian bắt đầu AP mode
unsigned long lastWiFiRetry = 0;    // Thời gian thử WiFi cuối cùng
bool wifiRetryInProgress = false;   // Đang thử kết nối WiFi

// Tạo web server trên port 80
ESP8266WebServer server(80);

// ===== CẤU HÌNH THIẾT BỊ =====
// Cảm biến siêu âm HY-SRF05
const int TRIG_PIN = 4;    // GPIO4 (D2 trên NodeMCU) - Chân Trigger
const int ECHO_PIN = 5;    // GPIO5 (D1 trên NodeMCU) - Chân Echo

// Bơm nước
const int PUMP_PIN = 14;   // GPIO14 (D5 trên NodeMCU) - Điều khiển relay bơm

// Nút vật lý
const int BUTTON_PIN = 12; // GPIO12 (D6 trên NodeMCU) - Nút nhấn giữ

// ===== BIẾN TRẠNG THÁI =====
float waterLevel = 0.0;           // Mực nước hiện tại (cm)
bool pumpState = false;           // Trạng thái bơm (bật/tắt)
bool buttonPressed = false;       // Trạng thái nút nhấn
unsigned long buttonPressTime = 0; // Thời gian bắt đầu nhấn nút
unsigned long lastSensorRead = 0; // Thời gian đọc cảm biến cuối cùng
const unsigned long SENSOR_INTERVAL = 1000; // Đọc cảm biến mỗi 1 giây
const unsigned long BUTTON_HOLD_TIME = 500; // Thời gian nhấn giữ tối thiểu (ms)

// Cấu hình mực nước
const float TANK_HEIGHT = 100.0;  // Chiều cao bể chứa (cm)
const float MIN_DISTANCE = 5.0;   // Khoảng cách tối thiểu từ cảm biến đến mặt nước (cm)
const float MAX_DISTANCE = 95.0;  // Khoảng cách tối đa từ cảm biến đến đáy bể (cm)

// Khai báo các hàm
void checkWiFiConnection();
void startStableAP();
void maintainAPConnection();
float readWaterLevel();
void controlPump(bool state);
void handleButtonPress();

// Hàm tạo trang HTML với điều khiển hệ thống đo mực nước
String getHTMLPage() {
  String html = "<!DOCTYPE html>";
  html += "<html lang='vi'>";
  html += "<head>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<title>Hệ Thống Đo Mực Nước</title>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #0a0a0a; color: #ffffff; }";
  html += ".container { max-width: 800px; margin: 0 auto; background: #1a1a1a; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.5); }";
  html += "h1 { color: #ffffff; text-align: center; margin-bottom: 30px; font-size: 28px; }";
  html += ".welcome { text-align: center; font-size: 16px; color: #b0b0b0; margin-bottom: 30px; }";
  html += ".devices { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }";
  html += ".device-card { background: #2a2a2a; border: 1px solid #404040; border-radius: 12px; padding: 20px; }";
  html += ".device-name { font-size: 18px; font-weight: bold; color: #ffffff; margin-bottom: 15px; display: flex; align-items: center; }";
  html += ".device-icon { margin-right: 10px; }";
  html += ".status-display { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 10px; background: #1a1a1a; border-radius: 8px; }";
  html += ".status-text { font-weight: bold; text-transform: uppercase; }";
  html += ".status-on { color: #00ff88; }";
  html += ".status-off { color: #808080; }";
  html += ".status-indicator { width: 10px; height: 10px; border-radius: 50%; }";
  html += ".indicator-on { background: #00ff88; box-shadow: 0 0 10px #00ff88; }";
  html += ".indicator-off { background: #808080; }";
  html += ".controls { display: flex; gap: 10px; flex-wrap: wrap; }";
  html += ".btn { flex: 1; min-width: 80px; padding: 12px 16px; border: none; border-radius: 8px; font-size: 14px; font-weight: bold; cursor: pointer; transition: all 0.3s; text-transform: uppercase; }";
  html += ".btn-on { background: #00ff88; color: #000; }";
  html += ".btn-on:hover { background: #00e67a; transform: translateY(-2px); }";
  html += ".btn-off { background: #ff4757; color: #fff; }";
  html += ".btn-off:hover { background: #ff3742; transform: translateY(-2px); }";
  html += ".water-level { font-size: 24px; text-align: center; margin: 15px 0; color: #00ff88; }";
  html += ".water-tank { width: 100%; height: 200px; background: #1a1a1a; border: 2px solid #404040; border-radius: 8px; position: relative; margin: 15px 0; }";
  html += ".water-fill { background: linear-gradient(to top, #0066cc, #00aaff); position: absolute; bottom: 0; width: 100%; border-radius: 0 0 6px 6px; transition: height 0.5s ease; }";
  html += ".tank-labels { display: flex; justify-content: space-between; margin-top: 10px; font-size: 12px; color: #b0b0b0; }";
  html += ".info { background: #2a2a2a; border: 1px solid #404040; padding: 20px; border-radius: 12px; }";
  html += ".info h3 { margin-top: 0; color: #00ff88; }";
  html += ".info p { margin: 8px 0; color: #b0b0b0; }";
  html += ".info strong { color: #ffffff; }";
  html += ".footer { text-align: center; margin-top: 30px; color: #808080; font-size: 14px; }";
  html += "@media (max-width: 768px) { .devices { grid-template-columns: 1fr; } .controls { flex-direction: column; } }";
  html += "</style>";
  html += "</head>";
  html += "<body>";
  html += "<div class='container'>";
  html += "<h1>💧 Hệ Thống Đo Mực Nước</h1>";
  html += "<div class='welcome'>";
  html += "Giám sát và điều khiển mực nước tự động • Truy cập qua http://" + String(mdns_name) + ".local";
  html += "</div>";

  // Phần điều khiển thiết bị
  html += "<div class='devices'>";

  // Card hiển thị mực nước
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>📏</span>Mực Nước";
  html += "</div>";
  html += "<div class='water-level'>";
  html += String(waterLevel, 1) + " cm";
  html += "</div>";
  html += "<div class='water-tank'>";
  float waterPercentage = ((TANK_HEIGHT - waterLevel) / TANK_HEIGHT) * 100;
  if (waterPercentage > 100) waterPercentage = 100;
  if (waterPercentage < 0) waterPercentage = 0;
  html += "<div class='water-fill' style='height: " + String(waterPercentage) + "%;'></div>";
  html += "</div>";
  html += "<div class='tank-labels'>";
  html += "<span>Trống</span>";
  html += "<span>Đầy</span>";
  html += "</div>";
  html += "</div>";

  // Card điều khiển bơm nước
  html += "<div class='device-card'>";
  html += "<div class='device-name'>";
  html += "<span class='device-icon'>🚰</span>Bơm Nước";
  html += "</div>";
  html += "<div class='status-display'>";
  html += "<span class='status-text " + String(pumpState ? "status-on" : "status-off") + "'>" + String(pumpState ? "ĐANG BƠM" : "DỪNG") + "</span>";
  html += "<div class='status-indicator " + String(pumpState ? "indicator-on" : "indicator-off") + "'></div>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn btn-on' onclick='controlPump(\"on\")'>BẬT BƠM</button>";
  html += "<button class='btn btn-off' onclick='controlPump(\"off\")'>TẮT BƠM</button>";
  html += "</div>";
  html += "<div style='margin-top: 15px; padding: 10px; background: #1a1a1a; border-radius: 8px; font-size: 14px; color: #b0b0b0;'>";
  html += "💡 <strong>Mẹo:</strong> Nhấn giữ nút vật lý để bơm nước thủ công";
  html += "</div>";
  html += "</div>";
  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>📡 Thông tin kết nối</h3>";

  if (isWiFiConnected) {
    html += "<p><strong>Chế độ:</strong> <span class='status connected'>WiFi Client</span></p>";
    html += "<p><strong>SSID:</strong> " + String(ssid) + "</p>";
    html += "<p><strong>Địa chỉ IP:</strong> " + WiFi.localIP().toString() + "</p>";
    html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
    html += "<p><strong>Gateway:</strong> " + WiFi.gatewayIP().toString() + "</p>";
    html += "<p><strong>Cường độ tín hiệu:</strong> " + String(WiFi.RSSI()) + " dBm</p>";
  } else if (isAPMode) {
    html += "<p><strong>Chế độ:</strong> <span class='status' style='background: #ffa502; color: #000;'>Access Point</span></p>";
    html += "<p><strong>AP SSID:</strong> " + String(ap_ssid) + "</p>";
    html += "<p><strong>AP IP:</strong> " + WiFi.softAPIP().toString() + "</p>";
    html += "<p><strong>mDNS:</strong> http://" + String(mdns_name) + ".local</p>";
    html += "<p><strong>Clients kết nối:</strong> " + String(WiFi.softAPgetStationNum()) + "</p>";
    html += "<p><strong>Trạng thái:</strong> Chờ kết nối WiFi...</p>";
  }

  html += "</div>";
  
  html += "<div class='info'>";
  html += "<h3>⚡ Thông tin hệ thống</h3>";
  html += "<p><strong>Thời gian hoạt động:</strong> " + String(millis() / 1000) + " giây</p>";
  html += "<p><strong>Bộ nhớ trống:</strong> " + String(ESP.getFreeHeap()) + " bytes</p>";
  html += "<p><strong>Tần số CPU:</strong> " + String(ESP.getCpuFreqMHz()) + " MHz</p>";
  html += "</div>";
  
  html += "<div class='footer'>";
  html += "Hệ Thống Đo Mực Nước ESP8266<br>";
  html += "Truy cập qua: <strong>http://" + String(mdns_name) + ".local</strong>";
  html += "</div>";
  html += "</div>";

  // JavaScript
  html += "<script>";
  html += "function controlPump(action) {";
  html += "  fetch('/pump/' + action)";
  html += "    .then(response => response.text())";
  html += "    .then(data => { setTimeout(() => location.reload(), 500); })";
  html += "    .catch(error => { console.error('Lỗi:', error); alert('Không thể kết nối!'); });";
  html += "}";
  html += "// Tự động cập nhật mực nước mỗi 2 giây";
  html += "setInterval(function() {";
  html += "  fetch('/api/waterlevel')";
  html += "    .then(response => response.json())";
  html += "    .then(data => {";
  html += "      document.querySelector('.water-level').textContent = data.level.toFixed(1) + ' cm';";
  html += "      let percentage = ((100 - data.level) / 100) * 100;";
  html += "      if (percentage > 100) percentage = 100;";
  html += "      if (percentage < 0) percentage = 0;";
  html += "      document.querySelector('.water-fill').style.height = percentage + '%';";
  html += "    })";
  html += "    .catch(error => console.error('Lỗi cập nhật mực nước:', error));";
  html += "}, 2000);";
  html += "</script>";
  html += "</body>";
  html += "</html>";

  return html;
}

// Hàm đọc mực nước từ cảm biến HY-SRF05
float readWaterLevel() {
  // Gửi xung trigger
  digitalWrite(TRIG_PIN, LOW);
  delayMicroseconds(2);
  digitalWrite(TRIG_PIN, HIGH);
  delayMicroseconds(10);
  digitalWrite(TRIG_PIN, LOW);

  // Đọc thời gian echo
  unsigned long duration = pulseIn(ECHO_PIN, HIGH, 30000); // Timeout 30ms

  if (duration == 0) {
    Serial.println("⚠️ Cảm biến không phản hồi!");
    return -1; // Lỗi đọc cảm biến
  }

  // Tính khoảng cách (cm)
  float distance = (duration * 0.034) / 2;

  // Giới hạn khoảng cách hợp lệ
  if (distance < MIN_DISTANCE) distance = MIN_DISTANCE;
  if (distance > MAX_DISTANCE) distance = MAX_DISTANCE;

  // Tính mực nước (khoảng cách từ đáy bể)
  float level = TANK_HEIGHT - distance;
  if (level < 0) level = 0;
  if (level > TANK_HEIGHT) level = TANK_HEIGHT;

  return level;
}

// Hàm điều khiển bơm nước
void controlPump(bool state) {
  pumpState = state;
  digitalWrite(PUMP_PIN, state ? HIGH : LOW);
  Serial.println("Bơm nước: " + String(state ? "BẬT" : "TẮT"));
}

// Hàm xử lý nút nhấn vật lý
void handleButtonPress() {
  bool currentButtonState = digitalRead(BUTTON_PIN) == LOW; // Nút nhấn kéo xuống GND

  if (currentButtonState && !buttonPressed) {
    // Bắt đầu nhấn nút
    buttonPressed = true;
    buttonPressTime = millis();
  } else if (!currentButtonState && buttonPressed) {
    // Thả nút
    buttonPressed = false;
    unsigned long pressDuration = millis() - buttonPressTime;

    if (pressDuration >= BUTTON_HOLD_TIME) {
      // Nhấn giữ đủ lâu, bật/tắt bơm
      controlPump(!pumpState);
      Serial.println("🔘 Nút nhấn: " + String(pumpState ? "Bật" : "Tắt") + " bơm nước");
    }
  }
}

// Xử lý trang chủ
void handleRoot() {
  server.send(200, "text/html", getHTMLPage());
}

// Xử lý bật bơm nước
void handlePumpOn() {
  controlPump(true);
  server.send(200, "text/plain", "PUMP ON");
}

// Xử lý tắt bơm nước
void handlePumpOff() {
  controlPump(false);
  server.send(200, "text/plain", "PUMP OFF");
}

// API trả về mực nước hiện tại (JSON)
void handleWaterLevelAPI() {
  String json = "{\"level\":" + String(waterLevel, 2) + ",\"pump\":" + String(pumpState ? "true" : "false") + "}";
  server.send(200, "application/json", json);
}

// Xử lý trang không tìm thấy
void handleNotFound() {
  String message = "Trang không tìm thấy!\n\n";
  message += "URI: " + server.uri() + "\n";
  message += "Method: " + String(server.method()) + "\n";
  message += "Arguments: " + String(server.args()) + "\n";

  for (uint8_t i = 0; i < server.args(); i++) {
    message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
  }

  server.send(404, "text/plain", message);
}

void setup() {
  // Khởi tạo Serial Monitor
  Serial.begin(115200);
  delay(100);
  Serial.println();
  Serial.println("========================================");
  Serial.println("💧 Hệ Thống Đo Mực Nước ESP8266");
  Serial.println("========================================");

  // Cấu hình chân cảm biến siêu âm HY-SRF05
  pinMode(TRIG_PIN, OUTPUT);
  pinMode(ECHO_PIN, INPUT);
  digitalWrite(TRIG_PIN, LOW);

  // Cấu hình chân bơm nước (relay)
  pinMode(PUMP_PIN, OUTPUT);
  digitalWrite(PUMP_PIN, LOW); // Tắt bơm ban đầu

  // Cấu hình chân nút nhấn
  pinMode(BUTTON_PIN, INPUT_PULLUP); // Sử dụng pull-up internal

  Serial.println("✅ Đã khởi tạo các chân GPIO");
  Serial.println("📏 Cảm biến: TRIG=" + String(TRIG_PIN) + ", ECHO=" + String(ECHO_PIN));
  Serial.println("🚰 Bơm nước: PIN=" + String(PUMP_PIN));
  Serial.println("🔘 Nút nhấn: PIN=" + String(BUTTON_PIN));

  // Thử kết nối WiFi
  Serial.print("📡 Đang thử kết nối WiFi: ");
  Serial.println(ssid);

  WiFi.mode(WIFI_STA);  // Chế độ Station (client)
  WiFi.begin(ssid, password);

  // Chờ kết nối WiFi trong 20 giây
  int attempts = 0;
  Serial.print("⏳ Đang kết nối");
  while (WiFi.status() != WL_CONNECTED && attempts < 40) {
    delay(500);
    Serial.print(".");
    attempts++;

    // Hiển thị tiến trình
    if (attempts % 10 == 0) {
      Serial.println();
      Serial.print("🔄 Thử lần " + String(attempts/10) + "/4");
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    // Kết nối WiFi thành công
    isWiFiConnected = true;
    Serial.println();
    Serial.println("✅ WiFi đã kết nối thành công!");
    Serial.println("========================================");
    Serial.println("📊 THÔNG TIN MẠNG:");
    Serial.print("🌐 Địa chỉ IP: ");
    Serial.println(WiFi.localIP());
    Serial.print("🚪 Gateway: ");
    Serial.println(WiFi.gatewayIP());
    Serial.print("📶 Cường độ tín hiệu: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
    Serial.println("========================================");

    // Khởi tạo mDNS
    Serial.print("🔧 Đang cấu hình mDNS: ");
    Serial.print(mdns_name);
    Serial.println(".local");

    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");

      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS!");
    }

  } else {
    // Không thể kết nối WiFi, chuyển sang chế độ AP
    Serial.println();
    Serial.println("❌ KHÔNG THỂ KẾT NỐI WIFI!");
    Serial.println("🔧 Nguyên nhân có thể:");
    Serial.println("   - Sai tên WiFi hoặc mật khẩu");
    Serial.println("   - WiFi không khả dụng");
    Serial.println("   - ESP8266 quá xa router");
    Serial.println();
    Serial.println("🔄 Chuyển sang chế độ Access Point...");

    // Chuyển sang chế độ AP với cấu hình ổn định
    startStableAP();
    isAPMode = true;
    apStartTime = millis();

    Serial.println("✅ Access Point đã được tạo!");
    Serial.println("========================================");
    Serial.println("📶 THÔNG TIN ACCESS POINT:");
    Serial.print("🏷️  SSID: ");
    Serial.println(ap_ssid);
    Serial.print("🔐 Password: ");
    Serial.println(ap_password);
    Serial.print("🌐 IP Address: ");
    Serial.println(WiFi.softAPIP());

    // Khởi tạo mDNS cho AP mode
    Serial.print("🔧 Đang cấu hình mDNS cho AP: ");
    Serial.print(mdns_name);
    Serial.println(".local");

    if (MDNS.begin(mdns_name)) {
      Serial.println("✅ mDNS đã khởi tạo thành công cho AP mode!");
      Serial.print("🌐 Truy cập qua: http://");
      Serial.print(mdns_name);
      Serial.println(".local");

      // Thêm service HTTP
      MDNS.addService("http", "tcp", 80);
      Serial.println("📡 HTTP service đã được đăng ký cho AP");
    } else {
      Serial.println("❌ Lỗi: Không thể khởi tạo mDNS cho AP mode!");
    }

    Serial.println("========================================");
    Serial.println("📱 HƯỚNG DẪN KẾT NỐI:");
    Serial.println("   1. Kết nối điện thoại/máy tính vào WiFi: " + String(ap_ssid));
    Serial.println("   2. Nhập mật khẩu: " + String(ap_password));
    Serial.println("   3. Truy cập bằng một trong các cách:");
    Serial.println("      • http://" + WiFi.softAPIP().toString());
    Serial.println("      • http://" + String(mdns_name) + ".local");
    Serial.println("   4. ESP8266 sẽ tự động thử kết nối WiFi lại sau 30 giây");
    Serial.println("========================================");
  }

  // Cấu hình web server (cho cả WiFi và AP mode)
  server.on("/", handleRoot);
  server.on("/pump/on", handlePumpOn);
  server.on("/pump/off", handlePumpOff);
  server.on("/api/waterlevel", handleWaterLevelAPI);
  server.onNotFound(handleNotFound);

  // Khởi động web server
  server.begin();
  Serial.println("🌐 Web server đã khởi động!");

  if (isWiFiConnected) {
    Serial.println("========================================");
    Serial.println("🎉 SẴN SÀNG SỬ DỤNG!");
    Serial.println("Truy cập bằng một trong các cách sau:");
    Serial.print("   • http://");
    Serial.println(WiFi.localIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
  } else if (isAPMode) {
    Serial.println("========================================");
    Serial.println("🎉 ACCESS POINT SẴN SÀNG!");
    Serial.println("Truy cập qua:");
    Serial.print("   • http://");
    Serial.println(WiFi.softAPIP());
    Serial.print("   • http://");
    Serial.print(mdns_name);
    Serial.println(".local");
    Serial.println("========================================");
  }
}

void loop() {
  // Xử lý các request từ web server
  server.handleClient();

  // Cập nhật mDNS (hoạt động trong cả WiFi và AP mode)
  MDNS.update();

  // Đọc cảm biến mực nước định kỳ
  if (millis() - lastSensorRead > SENSOR_INTERVAL) {
    lastSensorRead = millis();
    float newLevel = readWaterLevel();

    if (newLevel >= 0) { // Chỉ cập nhật nếu đọc thành công
      waterLevel = newLevel;
      Serial.println("💧 Mực nước: " + String(waterLevel, 1) + " cm");
    }
  }

  // Xử lý nút nhấn vật lý
  handleButtonPress();

  // Kiểm tra và duy trì kết nối AP nếu đang ở chế độ AP
  if (isAPMode) {
    maintainAPConnection();
  }

  // Kiểm tra và quản lý kết nối WiFi định kỳ
  static unsigned long lastCheck = 0;
  unsigned long checkInterval = isAPMode ? 120000 : 30000; // AP mode: 2 phút, WiFi mode: 30 giây

  if (millis() - lastCheck > checkInterval) {
    lastCheck = millis();
    checkWiFiConnection();
  }
}

// Hàm kiểm tra và quản lý kết nối WiFi
void checkWiFiConnection() {
  if (isWiFiConnected && WiFi.status() != WL_CONNECTED) {
    // Mất kết nối WiFi, chuyển sang AP mode
    Serial.println("⚠️  Mất kết nối WiFi! Chuyển sang chế độ Access Point...");
    isWiFiConnected = false;
    isAPMode = true;

    startStableAP();
    apStartTime = millis();

    Serial.println("✅ Access Point đã được kích hoạt!");
    Serial.println("📶 Kết nối vào WiFi: " + String(ap_ssid));
    Serial.println("🌐 Truy cập: http://" + WiFi.softAPIP().toString());
    Serial.println("🔧 Hoặc qua mDNS: http://" + String(mdns_name) + ".local");
  }
  else if (isAPMode && !isWiFiConnected) {
    // Đang ở chế độ AP, thử kết nối WiFi mà không làm gián đoạn AP

    // Chỉ thử kết nối WiFi mỗi 2 phút để tránh gián đoạn
    if (millis() - lastWiFiRetry > 120000 && !wifiRetryInProgress) {
      lastWiFiRetry = millis();
      wifiRetryInProgress = true;

      Serial.println("🔄 Thử kết nối WiFi (giữ nguyên AP mode)...");

      // Thử kết nối WiFi trong chế độ AP+STA
      WiFi.mode(WIFI_AP_STA);
      delay(100);

      // Khởi tạo lại AP sau khi chuyển mode
      WiFi.softAPConfig(IPAddress(192, 168, 4, 1), IPAddress(192, 168, 4, 1), IPAddress(255, 255, 255, 0));
      WiFi.softAP(ap_ssid, ap_password, ap_channel, ap_hidden, ap_max_connections);

      // Thử kết nối WiFi
      WiFi.begin(ssid, password);

      // Chờ tối đa 10 giây
      int attempts = 0;
      while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;

        // Xử lý web server trong khi chờ
        server.handleClient();
      }

      if (WiFi.status() == WL_CONNECTED) {
        // Kết nối WiFi thành công
        isWiFiConnected = true;
        isAPMode = false;
        Serial.println();
        Serial.println("✅ Đã kết nối WiFi thành công! Tắt AP mode...");
        Serial.println("🌐 IP Address: " + WiFi.localIP().toString());

        // Chuyển về chế độ WiFi thuần túy
        WiFi.mode(WIFI_STA);

        // Khởi tạo lại mDNS cho WiFi mode
        if (MDNS.begin(mdns_name)) {
          Serial.println("✅ mDNS đã được khởi tạo cho WiFi mode!");
          MDNS.addService("http", "tcp", 80);
        }
      } else {
        // Không kết nối được WiFi, giữ nguyên AP mode
        Serial.println();
        Serial.println("❌ Không thể kết nối WiFi, tiếp tục AP mode");

        // Đảm bảo AP vẫn hoạt động
        WiFi.mode(WIFI_AP);
        delay(100);
        startStableAP();
      }

      wifiRetryInProgress = false;
    }
  }
}

// Hàm khởi tạo Access Point ổn định
void startStableAP() {
  Serial.println("🔧 Khởi tạo Access Point ổn định...");

  // Dừng WiFi hiện tại
  WiFi.disconnect();
  WiFi.mode(WIFI_OFF);
  delay(100);

  // Cấu hình AP với các tham số ổn định
  WiFi.mode(WIFI_AP);
  delay(100);

  // Cấu hình IP tĩnh cho AP
  IPAddress apIP(192, 168, 4, 1);
  IPAddress gateway(192, 168, 4, 1);
  IPAddress subnet(255, 255, 255, 0);

  WiFi.softAPConfig(apIP, gateway, subnet);

  // Khởi tạo AP với cấu hình nâng cao
  bool apResult = WiFi.softAP(ap_ssid, ap_password, ap_channel, ap_hidden, ap_max_connections);

  if (apResult) {
    Serial.println("✅ AP khởi tạo thành công!");
    Serial.println("📊 Cấu hình AP:");
    Serial.println("   🏷️  SSID: " + String(ap_ssid));
    Serial.println("   🔐 Password: " + String(ap_password));
    Serial.println("   📡 Channel: " + String(ap_channel));
    Serial.println("   👥 Max Connections: " + String(ap_max_connections));
    Serial.println("   🌐 IP: " + WiFi.softAPIP().toString());

    // Khởi tạo mDNS cho AP
    delay(500); // Chờ AP ổn định
    if (MDNS.begin(mdns_name)) {
      MDNS.addService("http", "tcp", 80);
      Serial.println("✅ mDNS đã khởi tạo cho AP mode!");
    } else {
      Serial.println("⚠️  mDNS không khởi tạo được cho AP mode");
    }
  } else {
    Serial.println("❌ Lỗi khởi tạo AP!");
  }
}

// Hàm duy trì kết nối AP ổn định
void maintainAPConnection() {
  // Kiểm tra mỗi 10 giây (giảm tần suất để ổn định hơn)
  if (millis() - lastAPCheck > 10000) {
    lastAPCheck = millis();

    // Bỏ qua kiểm tra nếu đang thử kết nối WiFi
    if (wifiRetryInProgress) {
      return;
    }

    // Kiểm tra trạng thái AP
    wifi_softap_get_station_num(); // Refresh station count
    int connectedClients = WiFi.softAPgetStationNum();

    // Debug thông tin AP (chỉ khi có thay đổi)
    static int lastClientCount = -1;
    static unsigned long lastClientReport = 0;

    if (connectedClients != lastClientCount || millis() - lastClientReport > 60000) {
      Serial.println("📱 AP Status - Clients: " + String(connectedClients) +
                    ", Uptime: " + String((millis() - apStartTime) / 1000) + "s");
      lastClientCount = connectedClients;
      lastClientReport = millis();
    }

    // Kiểm tra nếu AP bị lỗi (không có IP hoặc không hoạt động)
    IPAddress apIP = WiFi.softAPIP();
    if (apIP == IPAddress(0, 0, 0, 0)) {
      Serial.println("⚠️  AP mất IP, khởi tạo lại...");
      startStableAP();
      apStartTime = millis();
      return;
    }

    // Chỉ restart AP nếu thực sự cần thiết (6 giờ không có client)
    unsigned long apUptime = millis() - apStartTime;
    if (apUptime > 21600000 && connectedClients == 0) { // 6 giờ không có client
      Serial.println("🔄 Restart AP sau 6 giờ không có client...");
      startStableAP();
      apStartTime = millis();
    }
  }
}
